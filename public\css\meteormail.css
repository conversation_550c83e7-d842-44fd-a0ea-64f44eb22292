@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&family=Quicksand:wght@400;500;600;700&display=swap');

/* 页面基础设置，修复溢出问题 */
html, body {
  overflow-x: hidden;
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overscroll-behavior: none; /* 防止iOS橡皮筋效果 */
}

:root {
  /* 共享颜色 */
  --primary: #4f46e5;
  --primary-dark: #4338ca;
  --primary-light: #6366f1;
  --accent: #c026d3;

  /* 深色主题变量 */
  /* 背景和表面 */
  --bg-color: #0f172a;
  --surface-color: #1e293b;
  --border-color: #334155;

  /* 文本颜色 */
  --text-primary: #ffffff;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;

  /* 卡片样式 */
  --card-bg: rgba(30, 41, 59, 0.4);
  --card-border: rgba(255, 255, 255, 0.03);

  /* 头部样式 */
  --header-bg: rgba(67, 56, 202, 0.4);
  --header-border: rgba(79, 70, 229, 0.4);

  /* 按钮样式 */
  --button-bg: #4f46e5;
  --button-hover: #4338ca;
  --button-text: #ffffff;

  /* 输入框样式 */
  --input-bg: rgba(30, 41, 59, 0.5);
  --input-border: rgba(79, 70, 229, 0.4);
  --input-text: #ffffff;

  /* 模态框样式 */
  --modal-bg: rgba(30, 41, 59, 0.8);
  --modal-border: #334155;

  /* 特效颜色 */
  --star-color: #ffffff;
  --meteor-color: #ffffff;
}

/* 浅色主题变量 */
:root.light {
  /* 背景和表面 */
  --bg-color: #eef2ff;
  --surface-color: #f5f7ff;
  --border-color: #d1d5db;

  /* 文本颜色 */
  --text-primary: #1e293b;
  --text-secondary: #4b5563;
  --text-tertiary: #6b7280;

  /* 卡片样式 */
  --card-bg: rgba(255, 255, 255, 0.5);
  --card-border: rgba(226, 232, 240, 0.5);

  /* 头部样式 */
  --header-bg: rgba(79, 70, 229, 0.1);
  --header-border: rgba(79, 70, 229, 0.2);

  /* 按钮样式 */
  --button-bg: #4f46e5;
  --button-hover: #4338ca;
  --button-text: #ffffff;

  /* 输入框样式 */
  --input-bg: rgba(255, 255, 255, 0.7);
  --input-border: rgba(79, 70, 229, 0.3);
  --input-text: #1e293b;

  /* 模态框样式 */
  --modal-bg: rgba(255, 255, 255, 0.7);
  --modal-border: rgba(226, 232, 240, 0.5);

  /* 特效颜色 */
  --star-color: #4f46e5;
  --meteor-color: #4f46e5;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Quicksand', -apple-system, BlinkMacSystemFont, sans-serif;
  min-height: 100vh;
  transition: background-color 0.3s ease;
  max-width: 100%;
}

h1, h2, h3, .brand-font {
  font-family: 'Montserrat', sans-serif;
}

/* Galaxy Background */
.galaxy-bg {
  background: linear-gradient(135deg, var(--bg-color), var(--surface-color), var(--bg-color));
  background-size: 200% 200%;
  color: var(--text-primary);
  min-height: 100vh;
  max-width: 100vw;
}

/* 页面容器修复 */
.min-h-screen {
  min-height: 100vh;
  max-width: 100%;
  position: relative;
  overflow-x: hidden;
}

/* Glass Card */
.glass-card {
  backdrop-filter: blur(16px);
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 16px;
}

/* 星星背景容器 */
#stars-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
  box-sizing: border-box;
}

/* 流星效果 - 从右上角到左下角 */
.meteor-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
  box-sizing: border-box;
}

.meteor {
  position: absolute;
  width: 300px; /* 增加流星长度 */
  height: 2px;
  transform: rotate(135deg); /* 从右上到左下的角度 */
  background-image: linear-gradient(to right, transparent, color-mix(in srgb, var(--meteor-color) 50%, transparent), var(--meteor-color));
  opacity: 0;
  top: calc(var(--top) * 1%);
  right: calc(var(--right) * 1%); /* 从右侧开始 */
  animation: meteor 4s linear forwards; /* 确保动画完成 */
  will-change: transform, opacity; /* 性能优化 */
}

@keyframes meteor {
  0% {
    opacity: 0;
    transform: translateX(0) translateY(0) rotate(135deg);
  }
  5% {
    opacity: 1;
  }
  40% {
    opacity: 1;
  }
  60% {
    opacity: 0;
  }
  100% {
    opacity: 0;
    transform: translateX(-800px) translateY(800px) rotate(135deg); /* 增加流星轨迹长度 */
  }
}

/* Stars */
.star {
  position: fixed;
  width: 2px;
  height: 2px;
  background-color: var(--star-color);
  border-radius: 50%;
  opacity: var(--opacity, 0.5); /* 默认值防止未设置 */
  animation: twinkle var(--duration, 4s) ease-in-out infinite;
  animation-delay: var(--delay, 0s);
  z-index: 0;
  pointer-events: none;
  will-change: opacity; /* 性能优化 */
}

@keyframes twinkle {
  0%, 100% { opacity: var(--min-opacity, 0.1); }
  50% { opacity: var(--max-opacity, 0.8); }
}

/* 按钮闪光效果 */
.btn-shine {
  position: relative;
  overflow: hidden;
}

/* 修复模态框溢出问题 */
#modal, #customModal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow-y: auto;
  max-height: 100vh;
  box-sizing: border-box;
}

/* 邮件内容区域滚动设置 */
#modalContent {
  max-height: 60vh;
  overflow-y: auto;
  overflow-x: hidden;
  word-break: break-word;
}

/* 确保原始内容区域也可以滚动 */
#rawContent {
  max-height: 40vh;
  overflow-y: auto;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background: color-mix(in srgb, var(--text-tertiary) 50%, transparent);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: color-mix(in srgb, var(--text-tertiary) 80%, transparent);
}

/* Custom selection */
::selection {
  background: color-mix(in srgb, var(--primary) 20%, transparent);
  color: var(--text-primary);
}

/* 语言切换按钮样式 */
#langToggle {
  position: relative;
}

#langToggle.zh-mode::after {
  content: 'CN';
  position: absolute;
  bottom: -2px;
  right: -2px;
  font-size: 8px;
  background-color: var(--primary);
  color: white;
  border-radius: 4px;
  padding: 1px 2px;
  line-height: 1;
}

#langToggle.en-mode::after {
  content: 'EN';
  position: absolute;
  bottom: -2px;
  right: -2px;
  font-size: 8px;
  background-color: var(--accent);
  color: white;
  border-radius: 4px;
  padding: 1px 2px;
  line-height: 1;
}

/* Responsive fixes */
@media (max-width: 640px) {
  .button-text {
    display: none;
  }

  .button-icon {
    margin-right: 0 !important;
  }

  .glass-card {
    border-radius: 12px;
  }
}