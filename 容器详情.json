{"Id": "a0ca87ae525f37b5c7c86f5e93c71cbc7eb467a3079a22ab33cc0c88ddf62377", "Created": "2025-08-01T01:51:22.024290036Z", "Path": "docker-entrypoint.sh", "Args": ["bash", "/run.sh"], "State": {"Status": "running", "Running": true, "Paused": false, "Restarting": false, "OOMKilled": false, "Dead": false, "Pid": 976954, "ExitCode": 0, "Error": "", "StartedAt": "2025-08-01T01:51:22.310678965Z", "FinishedAt": "0001-01-01T00:00:00Z"}, "Image": "sha256:5daeeb32f50ec114c9811adce0ca778f5f232165f2ef1843d0027ab18a66b75e", "ResolvConfPath": "/var/lib/docker/containers/a0ca87ae525f37b5c7c86f5e93c71cbc7eb467a3079a22ab33cc0c88ddf62377/resolv.conf", "HostnamePath": "/var/lib/docker/containers/a0ca87ae525f37b5c7c86f5e93c71cbc7eb467a3079a22ab33cc0c88ddf62377/hostname", "HostsPath": "/var/lib/docker/containers/a0ca87ae525f37b5c7c86f5e93c71cbc7eb467a3079a22ab33cc0c88ddf62377/hosts", "LogPath": "/var/lib/docker/containers/a0ca87ae525f37b5c7c86f5e93c71cbc7eb467a3079a22ab33cc0c88ddf62377/a0ca87ae525f37b5c7c86f5e93c71cbc7eb467a3079a22ab33cc0c88ddf62377-json.log", "Name": "/mail", "RestartCount": 0, "Driver": "overlay2", "Platform": "linux", "MountLabel": "", "ProcessLabel": "", "AppArmorProfile": "docker-default", "ExecIDs": null, "HostConfig": {"Binds": ["/opt/1panel/runtime/node/mail/run.sh:/run.sh:rw", "/opt/1panel/runtime/node/mail/.env:/.env:rw", "/opt/1panel/apps/openresty/openresty/www/sites/linshimail:/app:rw"], "ContainerIDFile": "", "LogConfig": {"Type": "json-file", "Config": {}}, "NetworkMode": "aea04325569ba95f58137598387175b99bc48a8f71e2415a670efd18b681584d", "PortBindings": {"25/tcp": [{"HostIp": "0.0.0.0", "HostPort": "25"}], "8118/tcp": [{"HostIp": "0.0.0.0", "HostPort": "8118"}]}, "RestartPolicy": {"Name": "on-failure", "MaximumRetryCount": 5}, "AutoRemove": false, "VolumeDriver": "", "VolumesFrom": null, "ConsoleSize": [0, 0], "CapAdd": null, "CapDrop": null, "CgroupnsMode": "private", "Dns": null, "DnsOptions": null, "DnsSearch": null, "ExtraHosts": [], "GroupAdd": null, "IpcMode": "private", "Cgroup": "", "Links": null, "OomScoreAdj": 0, "PidMode": "", "Privileged": false, "PublishAllPorts": false, "ReadonlyRootfs": false, "SecurityOpt": null, "UTSMode": "", "UsernsMode": "", "ShmSize": 67108864, "Runtime": "runc", "Isolation": "", "CpuShares": 0, "Memory": 0, "NanoCpus": 0, "CgroupParent": "", "BlkioWeight": 0, "BlkioWeightDevice": null, "BlkioDeviceReadBps": null, "BlkioDeviceWriteBps": null, "BlkioDeviceReadIOps": null, "BlkioDeviceWriteIOps": null, "CpuPeriod": 0, "CpuQuota": 0, "CpuRealtimePeriod": 0, "CpuRealtimeRuntime": 0, "CpusetCpus": "", "CpusetMems": "", "Devices": null, "DeviceCgroupRules": null, "DeviceRequests": null, "MemoryReservation": 0, "MemorySwap": 0, "MemorySwappiness": null, "OomKillDisable": null, "PidsLimit": null, "Ulimits": null, "CpuCount": 0, "CpuPercent": 0, "IOMaximumIOps": 0, "IOMaximumBandwidth": 0, "MaskedPaths": ["/proc/asound", "/proc/acpi", "/proc/kcore", "/proc/keys", "/proc/latency_stats", "/proc/timer_list", "/proc/timer_stats", "/proc/sched_debug", "/proc/scsi", "/sys/firmware", "/sys/devices/virtual/powercap"], "ReadonlyPaths": ["/proc/bus", "/proc/fs", "/proc/irq", "/proc/sys", "/proc/sysrq-trigger"]}, "GraphDriver": {"Data": {"LowerDir": "/var/lib/docker/overlay2/b256e33617f03f9578dcdedbe8cf70af73ac474d45636b7882c7b069442f8d50-init/diff:/var/lib/docker/overlay2/96d1dc0a0012151fc4c3c599b69bc70e5a29852273b2c626ab5e85d22fc819a4/diff:/var/lib/docker/overlay2/ae3329ea62db74463437365666db3d0264f9c3ea5d7186db2d0b74707efc970a/diff:/var/lib/docker/overlay2/fcd018615757148308bc7f5ec864c6ec9ef7f13d25edee9107eeedf93ea74b8c/diff:/var/lib/docker/overlay2/19b9380e21fd55f65710435d08524fa136265d22acb3e8947e29feb846206c9d/diff:/var/lib/docker/overlay2/2394db113b22fcbc6dc0d8dd527dd7a5abd8b7d281d21b90762f5fe53cd04eff/diff:/var/lib/docker/overlay2/a7f5df5ddb02f7f124147a326f6a2f909175d5ddb3ca6d898bf67e98f433ea40/diff:/var/lib/docker/overlay2/45b3c4e61deeba44cfd39bc510bea2d75403d9b34499b26a5c413d952b25852d/diff:/var/lib/docker/overlay2/056b3e4288b2be378fe002743aa1d8d7f133a5a4c0627d8c973d71edb15ee630/diff:/var/lib/docker/overlay2/9ecbd6b0fd802653e2a8207514392aa4d716a9f52a33da11192d0bc675b78bad/diff:/var/lib/docker/overlay2/7dc46dab5fb4e0af40077cca2bbf2faf23a53f8b19c89521311bafc51930c497/diff:/var/lib/docker/overlay2/f9ecb66100d33dd14fc238919f8c8eef00d2438a3107260d38c4ff1ea6718db8/diff", "MergedDir": "/var/lib/docker/overlay2/b256e33617f03f9578dcdedbe8cf70af73ac474d45636b7882c7b069442f8d50/merged", "UpperDir": "/var/lib/docker/overlay2/b256e33617f03f9578dcdedbe8cf70af73ac474d45636b7882c7b069442f8d50/diff", "WorkDir": "/var/lib/docker/overlay2/b256e33617f03f9578dcdedbe8cf70af73ac474d45636b7882c7b069442f8d50/work"}, "Name": "overlay2"}, "Mounts": [{"Type": "bind", "Source": "/opt/1panel/runtime/node/mail/run.sh", "Destination": "/run.sh", "Mode": "rw", "RW": true, "Propagation": "rprivate"}, {"Type": "bind", "Source": "/opt/1panel/runtime/node/mail/.env", "Destination": "/.env", "Mode": "rw", "RW": true, "Propagation": "rprivate"}, {"Type": "bind", "Source": "/opt/1panel/apps/openresty/openresty/www/sites/linshimail", "Destination": "/app", "Mode": "rw", "RW": true, "Propagation": "rprivate"}], "Config": {"Hostname": "b11140a2964b", "Domainname": "", "User": "", "AttachStdin": false, "AttachStdout": true, "AttachStderr": true, "ExposedPorts": {"25/tcp": {}, "8118/tcp": {}}, "Tty": false, "OpenStdin": false, "StdinOnce": false, "Env": ["PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin", "NODE_VERSION=22.17.0", "YARN_VERSION=1.22.22"], "Cmd": ["bash", "/run.sh"], "Image": "1panel/node:22.17.0", "Volumes": null, "WorkingDir": "/app", "Entrypoint": ["docker-entrypoint.sh"], "OnBuild": null, "Labels": {"com.docker.compose.config-hash": "44428bc7f2645e4076251a9477a2616fc0a1295c023ddae2f36cbab03df09a6e", "com.docker.compose.container-number": "1", "com.docker.compose.depends_on": "", "com.docker.compose.image": "sha256:5daeeb32f50ec114c9811adce0ca778f5f232165f2ef1843d0027ab18a66b75e", "com.docker.compose.oneoff": "False", "com.docker.compose.project": "mail", "com.docker.compose.project.config_files": "/opt/1panel/runtime/node/mail/docker-compose.yml", "com.docker.compose.project.working_dir": "/opt/1panel/runtime/node/mail", "com.docker.compose.service": "node", "com.docker.compose.version": "2.26.1", "createdBy": "Apps"}}, "NetworkSettings": {"Bridge": "", "SandboxID": "aacce34bed3abaf72b2fd3d5a3c3d0479635203f0d27749d7ef91fba28b25aa5", "SandboxKey": "/var/run/docker/netns/aacce34bed3a", "Ports": {"25/tcp": [{"HostIp": "0.0.0.0", "HostPort": "25"}], "8118/tcp": [{"HostIp": "0.0.0.0", "HostPort": "8118"}]}, "HairpinMode": false, "LinkLocalIPv6Address": "", "LinkLocalIPv6PrefixLen": 0, "SecondaryIPAddresses": null, "SecondaryIPv6Addresses": null, "EndpointID": "", "Gateway": "", "GlobalIPv6Address": "", "GlobalIPv6PrefixLen": 0, "IPAddress": "", "IPPrefixLen": 0, "IPv6Gateway": "", "MacAddress": "", "Networks": {"1panel-network": {"IPAMConfig": {"IPv4Address": "**********"}, "Links": null, "Aliases": null, "MacAddress": "02:42:ac:12:00:04", "NetworkID": "aea04325569ba95f58137598387175b99bc48a8f71e2415a670efd18b681584d", "EndpointID": "c9f5d38a11905a3e89a106c615575ac7fbf850095db4ddf19523fd71b5f75c62", "Gateway": "**********", "IPAddress": "**********", "IPPrefixLen": 16, "IPv6Gateway": "", "GlobalIPv6Address": "", "GlobalIPv6PrefixLen": 0, "DriverOpts": null, "DNSNames": ["mail", "a0ca87ae525f", "b11140a2964b"]}}}}