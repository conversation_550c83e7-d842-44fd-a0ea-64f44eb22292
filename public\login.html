<!DOCTYPE html>
<html lang="zh-CN" class="dark">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>管理员登录 - MeteorMail</title>
  <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
  <link rel="stylesheet" href="/css/meteormail.css">
</head>
<body class="galaxy-bg text-gray-100 antialiased overflow-x-hidden">
  <!-- 星星背景 -->
  <div id="stars-container"></div>
  <div class="meteor-container" id="meteor-container"></div>

  <div class="min-h-screen flex flex-col items-center justify-center relative z-10 p-4">
    
    <!-- 切换按钮 -->
    <div class="absolute top-4 right-4 flex items-center space-x-2">
        <button id="langToggle" class="p-2 rounded-full transition focus:outline-none hover:opacity-80" style="background-color: var(--button-bg);" aria-label="切换语言" title="切换语言">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" style="color: var(--button-text);" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389 16.87 16.87 0 01-.554-.514 19.05 19.05 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.054 17.054 0 003.07-3.293 18.013 18.013 0 01-1.487-2.594 1 1 0 111.79-.894c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clip-rule="evenodd" /></svg>
        </button>
        <button id="darkToggle" class="p-2 rounded-full transition focus:outline-none hover:opacity-80" style="background-color: var(--button-bg);" aria-label="切换深色模式" title="切换深色模式">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" style="color: var(--button-text);" viewBox="0 0 20 20" fill="currentColor"><path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" /></svg>
        </button>
    </div>

    <div class="w-full max-w-md">
      <div class="text-center mb-8">
        <a href="/" class="flex items-center justify-center" aria-label="MeteorMail">
          <svg class="w-10 h-10" style="color: var(--primary);" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z" stroke="currentColor" stroke-width="2"/><path d="M18 7L14 14L8 9L6 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>
          <span class="ml-3 text-3xl font-bold brand-font" style="color: var(--text-primary);">MeteorMail</span>
        </a>
        <h1 class="text-2xl font-bold mt-4" style="color: var(--text-primary);" data-i18n="admin_login">管理员登录</h1>
      </div>

      <div class="glass-card p-8">
        <div id="error-message" class="hidden bg-red-900 bg-opacity-50 text-red-300 text-sm rounded-lg p-3 mb-4 text-center">
            <!-- 错误信息将显示在这里 -->
        </div>
        <form action="/admin/login" method="POST">
          <div class="mb-5">
            <label for="username" class="block mb-2 text-sm font-medium" style="color: var(--text-secondary);" data-i18n="username">用户名</label>
            <input type="text" id="username" name="username" class="block w-full px-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500" style="background: var(--input-bg); border: 1px solid var(--input-border); color: var(--input-text);" required>
          </div>
          <div class="mb-6">
            <label for="password" class="block mb-2 text-sm font-medium" style="color: var(--text-secondary);" data-i18n="password">密码</label>
            <input type="password" id="password" name="password" class="block w-full px-4 py-3 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500" style="background: var(--input-bg); border: 1px solid var(--input-border); color: var(--input-text);" required>
          </div>
          <button type="submit" class="w-full flex items-center justify-center px-4 py-3 rounded-xl font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 hover:opacity-90" style="background-color: var(--primary); color: white;" data-i18n="login_button">
            登 录
          </button>
        </form>
      </div>
       <footer class="py-6 text-center text-xs sm:text-sm mt-auto w-full" style="color: var(--text-tertiary);">
        <p><a href="/" data-i18n="nav_home">返回首页</a></p>
      </footer>
    </div>
  </div>

  <script src="/js/meteor-effects.js"></script>
  <script src="/js/translations.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', () => {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('error')) {
            const errorDiv = document.getElementById('error-message');
            const lang = localStorage.getItem('language') || 'zh-CN';
            const trans = window.translations || {};
            const errorMessage = (trans[lang] && trans[lang].login_error) 
                ? trans[lang].login_error 
                : (lang === 'en' ? 'Invalid username or password.' : '用户名或密码错误。');
            
            errorDiv.textContent = errorMessage;
            errorDiv.classList.remove('hidden');
        }
        
        // 初始化语言和主题
        if(typeof setupLanguage === 'function') {
            setupLanguage();
        }
        if(typeof setupTheme === 'function') {
            setupTheme();
        }
    });
  </script>
</body>
</html> 