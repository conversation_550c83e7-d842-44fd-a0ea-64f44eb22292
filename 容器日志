up to date, audited 141 packages in 1s

27 packages are looking for funding
  run `npm fund` for details

2 low severity vulnerabilities

To address all issues, run:
  npm audit fix

Run `npm audit` for details.

> meteormail@1.0.0 start
> node src/server.js

成功加载 config.json 文件
服务已启动，端口: 8118
SMTP服务已启动，端口: 25, 地址: 0.0.0.0
新客户端连接: tByoT78aSLYtIiJQAAAB
客户端断开: tByoT78aSLYtIiJQAAAB
新客户端连接: 9exuxTC9EpVOasn2AAAD
有SMTP客户端连接: *************
开始接收邮件，收件人: <EMAIL>
邮件已保存: 五千
客户端断开: 9exuxTC9EpVOasn2AAAD
新客户端连接: TYYBhdy174gTL0HvAAAF
